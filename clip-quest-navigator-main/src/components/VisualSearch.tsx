import React, { useState } from 'react';
import { Search, Eye, Clock, Zap, Film, ChevronDown, ChevronUp, Users, Target } from 'lucide-react';
import { SearchResult, ClipResult } from '@/services/api';
import { useToast } from '@/hooks/use-toast';

interface VisualSearchProps {
  videoId: number;
  videoUrl: string;
  onTimeJump: (time: number) => void;
}

const VisualSearch: React.FC<VisualSearchProps> = ({ videoId, onTimeJump }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchClips, setSearchClips] = useState<ClipResult[]>([]);
  const [hasSearched, setHasSearched] = useState(false);
  const [viewMode, setViewMode] = useState<'clips' | 'frames'>('clips');
  const [directAnswer, setDirectAnswer] = useState<string>('');
  const [queryType, setQueryType] = useState<string>('');
  const [expandedResults, setExpandedResults] = useState<Set<number>>(new Set());

  const { toast } = useToast();

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setHasSearched(true);
    setExpandedResults(new Set()); // Reset expanded results

    try {
      // Use real backend API
      const response = await apiService.visualSearch(videoId, searchQuery);

      // Check if backend returns enhanced format
      if (response.direct_answer || response.query_type || response.clips) {
        // Backend supports enhanced format
        setSearchResults(response.results);
        setSearchClips(response.clips || []);
        setDirectAnswer(response.direct_answer || '');
        setQueryType(response.query_type || '');
      } else {
        // Backend returns old format, convert to enhanced
        setSearchResults(response.results.map(result => ({
          ...result,
          summary: result.description.length > 100 ? 
            result.description.substring(0, 100) + '...' : 
            result.description,
          detailed_analysis: result.description
        })));
        setSearchClips([]);
        setDirectAnswer(response.results.length > 0 ? 
          `Found ${response.results.length} matches for "${searchQuery}"` : '');
        setQueryType('general');
      }

      if (response.results.length === 0) {
        toast({
          title: "No results found",
          description: `No visual content matching "${searchQuery}" was found in this video.`,
        });
      }
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Search failed",
        description: error instanceof Error ? error.message : "Failed to search video content",
        variant: "destructive",
      });

      // Only fallback to mock if there's a real error
      setSearchResults([]);
      setSearchClips([]);
      setDirectAnswer('');
      setQueryType('');
    } finally {
      setIsSearching(false);
    }
  };

  const generateEnhancedMockResults = (query: string): {
    results: SearchResult[];
    clips: ClipResult[];
    direct_answer?: string;
    query_type?: string;
  } => {
    const lowerQuery = query.toLowerCase();
    console.log('🔍 Enhanced search for:', query, '| Lower:', lowerQuery);

    // Counting queries - Enhanced to catch your specific query
    if (lowerQuery.includes('how many') || lowerQuery.includes('count') || lowerQuery.includes('number of') ||
        lowerQuery.includes('people is here') || lowerQuery.includes('people are here') ||
        lowerQuery.includes('many people') || lowerQuery.includes('people here')) {
      if (lowerQuery.includes('people') || lowerQuery.includes('person') || lowerQuery.includes('speaker') || lowerQuery.includes('here')) {
        console.log('✅ Triggered counting query for people!');
        return {
          results: [
        {
          timestamp: 15,
          confidence: 95,
              description: 'One adult male speaker introducing himself at video start',
              summary: '1 person detected - adult male speaker',
              people_count: 1,
              objects_detected: ['microphone', 'chair', 'person'],
              detailed_analysis: 'Professional studio setup with one male speaker, approximately 30-50 years old, wearing black shirt, positioned in front of microphone for recording.',
          frame_path: '/api/placeholder/120/68'
        },
        {
          timestamp: 45,
          confidence: 92,
              description: 'Same speaker explaining his background and experience',
              summary: '1 person detected - same male speaker',
              people_count: 1,
              objects_detected: ['microphone', 'chair', 'person'],
              detailed_analysis: 'Continuation of recording session with the same individual speaker discussing his professional background.',
              frame_path: '/api/placeholder/120/68'
            },
            {
              timestamp: 120,
              confidence: 94,
              description: 'Speaker demonstrating project features and concepts',
              summary: '1 person detected - presenter mode',
              people_count: 1,
              objects_detected: ['microphone', 'person', 'studio setup'],
              detailed_analysis: 'The speaker is actively presenting and explaining technical concepts, maintaining consistent positioning in the professional recording environment.',
          frame_path: '/api/placeholder/120/68'
        },
        {
          timestamp: 180,
              confidence: 91,
              description: 'Speaker continuing technical discussion',
              summary: '1 person detected - technical explanation',
              people_count: 1,
              objects_detected: ['microphone', 'person', 'boom arm'],
              detailed_analysis: 'The same individual continues the presentation, discussing technical implementation details with clear visibility throughout.',
              frame_path: '/api/placeholder/120/68'
            },
            {
              timestamp: 240,
              confidence: 89,
              description: 'Speaker in final segments of presentation',
              summary: '1 person detected - conclusion',
              people_count: 1,
              objects_detected: ['microphone', 'person'],
              detailed_analysis: 'The speaker concludes the presentation, maintaining the same professional setup and positioning throughout the entire video.',
              frame_path: '/api/placeholder/120/68'
            }
          ],
          clips: [],
          direct_answer: '1 person detected consistently throughout the video (5 instances)',
          query_type: 'counting'
        };
      }

      if (lowerQuery.includes('object') || lowerQuery.includes('item')) {
        return {
          results: [
            {
              timestamp: 30,
          confidence: 88,
              description: 'Multiple objects visible in frame',
              summary: '3-4 main objects detected',
              objects_detected: ['microphone', 'chair', 'boom arm', 'clothing'],
              detailed_analysis: 'Professional recording setup with microphone, boom arm, upholstered chair, and speaker clothing visible.',
          frame_path: '/api/placeholder/120/68'
        }
          ],
          clips: [],
          direct_answer: '3-4 main objects detected (microphone, chair, boom arm, clothing)',
          query_type: 'counting'
        };
      }
    }

    // Object detection queries
    if (lowerQuery.includes('microphone') || lowerQuery.includes('mic')) {
      console.log('✅ Triggered microphone detection query!');
      return {
        results: [
          {
            timestamp: 10,
            confidence: 98,
            description: 'Professional broadcast microphone visible at video start',
            summary: 'Shure SM7B microphone detected',
            objects_detected: ['microphone', 'boom arm', 'shock mount'],
            detailed_analysis: 'High-quality Shure SM7B dynamic microphone with shock mount, positioned on articulated boom arm for professional recording. Clear view of SHURE branding and SM7B model number.',
            frame_path: '/api/placeholder/120/68'
          },
          {
            timestamp: 45,
            confidence: 96,
            description: 'Microphone remains prominently visible during speaker introduction',
            summary: 'Shure SM7B microphone - clear view',
            objects_detected: ['microphone', 'boom arm', 'shock mount', 'cables'],
            detailed_analysis: 'Continued clear view of the professional microphone setup. The boom arm positioning and shock mount are clearly visible, indicating professional podcast/recording environment.',
            frame_path: '/api/placeholder/120/68'
          },
          {
            timestamp: 90,
            confidence: 94,
            description: 'Microphone visible as speaker discusses background',
            summary: 'Shure SM7B microphone - side angle',
            objects_detected: ['microphone', 'boom arm', 'branding'],
            detailed_analysis: 'Side angle view of the microphone showing the distinctive Shure SM7B design. The boom arm articulation and professional mounting system are clearly visible.',
            frame_path: '/api/placeholder/120/68'
          },
          {
            timestamp: 150,
            confidence: 92,
            description: 'Microphone setup visible during project explanation',
            summary: 'Shure SM7B microphone - recording setup',
            objects_detected: ['microphone', 'boom arm', 'shock mount'],
            detailed_analysis: 'Professional recording setup with microphone positioned optimally for voice capture. The shock mount system is clearly visible, designed to reduce vibrations and handling noise.',
            frame_path: '/api/placeholder/120/68'
          },
          {
            timestamp: 210,
            confidence: 90,
            description: 'Microphone remains in frame during technical discussion',
            summary: 'Shure SM7B microphone - consistent presence',
            objects_detected: ['microphone', 'boom arm', 'studio setup'],
            detailed_analysis: 'The microphone maintains consistent positioning throughout the recording, indicating a well-planned studio setup. Professional broadcast-quality equipment clearly visible.',
            frame_path: '/api/placeholder/120/68'
          },
          {
            timestamp: 280,
            confidence: 88,
            description: 'Microphone visible in final segments of presentation',
            summary: 'Shure SM7B microphone - end segment',
            objects_detected: ['microphone', 'boom arm'],
            detailed_analysis: 'Final clear view of the microphone setup as the presentation concludes. The professional audio equipment remains consistently positioned throughout the entire recording.',
            frame_path: '/api/placeholder/120/68'
          }
        ],
        clips: [],
        direct_answer: 'Professional Shure SM7B microphone detected throughout video (6 instances)',
        query_type: 'object_detection'
      };
    }

    // Clothing/appearance queries
    if (lowerQuery.includes('wearing') || lowerQuery.includes('shirt') || lowerQuery.includes('clothes')) {
      return {
        results: [
          {
            timestamp: 20,
            confidence: 94,
            description: 'Speaker wearing black henley shirt',
            summary: 'Black henley-style shirt with buttons',
            objects_detected: ['black shirt', 'buttons', 'chain necklace'],
            detailed_analysis: 'Speaker is wearing a black henley-style shirt with visible collar and buttons near neckline, plus a thin metallic chain.',
            frame_path: '/api/placeholder/120/68'
          }
        ],
        clips: [],
        direct_answer: 'Black henley-style shirt with buttons and thin chain necklace',
        query_type: 'object_detection'
      };
    }

    // Background/scene queries
    if (lowerQuery.includes('background') || lowerQuery.includes('color') || lowerQuery.includes('scene')) {
      return {
        results: [
          {
            timestamp: 25,
            confidence: 91,
            description: 'Professional studio setup with solid background',
            summary: 'Dark green/teal solid background',
            objects_detected: ['background', 'studio lighting'],
            detailed_analysis: 'Professional recording environment with uniform dark forest green or deep teal background, designed to minimize distractions.',
            frame_path: '/api/placeholder/120/68'
          }
        ],
        clips: [],
        direct_answer: 'Dark green/teal solid background in professional studio setting',
        query_type: 'scene_analysis'
      };
    }

    // Default enhanced results
    return {
      results: [
        {
        timestamp: 60,
        confidence: 75,
        description: `Content related to "${query}" found in video`,
          summary: `Match found for "${query}"`,
          detailed_analysis: `Detailed analysis available for content matching "${query}" in the video frame.`,
          frame_path: '/api/placeholder/120/68'
        }
      ],
      clips: [],
      direct_answer: `Found content matching "${query}"`,
      query_type: 'general'
    };
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 90) return 'text-green-400';
    if (confidence >= 75) return 'text-yellow-400';
    return 'text-orange-400';
  };

  const toggleExpandResult = (index: number) => {
    const newExpanded = new Set(expandedResults);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedResults(newExpanded);
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-foreground mb-2">Visual Search</h3>
        <p className="text-sm text-muted-foreground">
          Search for objects, people, or scenes within video frames
        </p>
      </div>

      {/* Search Form */}
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Describe what you're looking for... (e.g., 'red car', 'person speaking', 'computer screen')"
            className="w-full px-4 py-3 pl-12 bg-input border border-border rounded-lg text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
            disabled={isSearching}
          />
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
        </div>

        <button
          type="submit"
          disabled={!searchQuery.trim() || isSearching}
          className="w-full bg-primary hover:bg-primary/90 disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed text-primary-foreground font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center gap-2"
        >
          {isSearching ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-primary-foreground border-t-transparent"></div>
              Analyzing Frames...
            </>
          ) : (
            <>
              <Eye className="w-5 h-5" />
              Search Video
            </>
          )}
        </button>
      </form>

      {/* Search Results */}
      {hasSearched && (
        <div className="space-y-4">
          {isSearching ? (
            <div className="text-center py-8">
              <div className="animate-pulse space-y-4">
                <div className="w-16 h-16 bg-gray-800 rounded-full mx-auto flex items-center justify-center">
                  <Zap className="w-8 h-8 text-white" />
                </div>
                <p className="text-gray-300">
                  AI is analyzing video frames for "{searchQuery}"
                </p>
              </div>
            </div>
          ) : searchResults.length > 0 ? (
            <>
              {/* Direct Answer Section */}
              {directAnswer && (
                <div className="mb-4 p-4 bg-gray-900/80 rounded-lg border border-gray-700">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center flex-shrink-0">
                      {queryType === 'counting' ? (
                        <Users className="w-4 h-4 text-black" />
                      ) : (
                        <Target className="w-4 h-4 text-black" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-white font-medium mb-1">Direct Answer</h4>
                      <p className="text-gray-300 text-sm leading-relaxed">{directAnswer}</p>
                      {queryType && (
                        <span className="inline-block mt-2 px-2 py-1 bg-gray-800 rounded text-xs text-gray-400 border border-gray-600">
                          {queryType.replace('_', ' ')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Results Header with View Toggle */}
              <div className="flex items-center justify-between mb-4">
                <div>
                <h4 className="text-white font-medium">
                    Found {searchResults.length} {searchResults.length === 1 ? 'match' : 'matches'} for "{searchQuery}"
                </h4>
                  <p className="text-sm text-gray-400 mt-1">
                    {searchResults.length > 1 ? 'Multiple instances detected throughout the video. ' : ''}Click any result to jump to that moment in the video
                  </p>
                </div>
                {searchClips.length > 0 && (
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setViewMode('clips')}
                      className={`px-3 py-1 rounded text-sm font-medium transition-all ${
                        viewMode === 'clips'
                          ? 'bg-white text-black'
                          : 'bg-gray-800 text-gray-400 hover:bg-gray-700 hover:text-white'
                      }`}
                    >
                      <Film className="w-4 h-4 inline mr-1" />
                      Clips ({searchClips.length})
                    </button>
                    <button
                      onClick={() => setViewMode('frames')}
                      className={`px-3 py-1 rounded text-sm font-medium transition-all ${
                        viewMode === 'frames'
                          ? 'bg-white text-black'
                          : 'bg-gray-800 text-gray-400 hover:bg-gray-700 hover:text-white'
                      }`}
                    >
                      <Eye className="w-4 h-4 inline mr-1" />
                      Frames ({searchResults.length})
                    </button>
                  </div>
                )}
              </div>

              {/* Clips View */}
              {viewMode === 'clips' && searchClips.length > 0 && (
                <div className="max-h-96 overflow-y-auto space-y-3 pr-2 scrollbar-thin scrollbar-thumb-gray-500/50 scrollbar-track-transparent">
                  {searchClips.map((clip, index) => (
                    <div
                      key={index}
                      className="bg-card rounded-lg border border-border p-4 hover:bg-muted/50 transition-all duration-300"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1 pr-4">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-xs text-muted-foreground font-medium">#{index + 1}</span>
                            <h5 className="text-foreground font-medium">
                              {formatTime(clip.start_time)} - {formatTime(clip.end_time)}
                            </h5>
                            <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                              {Math.round((clip.end_time - clip.start_time) * 10) / 10}s
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground leading-relaxed">
                            {clip.description}
                          </p>
                        </div>
                        <div className="flex flex-col items-end gap-1">
                          <span className={`text-sm font-medium px-2 py-1 rounded ${getConfidenceColor(clip.confidence)} bg-muted`}>
                            {clip.confidence}%
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {clip.frame_count} frames
                          </span>
                        </div>
                      </div>

                      {/* Clip Actions */}
                      <div className="flex gap-2">
                        <button
                          onClick={() => onTimeJump(clip.start_time)}
                          className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-2 rounded text-sm font-medium transition-colors flex items-center justify-center gap-1"
                        >
                          <Eye className="w-4 h-4" />
                          Play from start
                        </button>
                        <button
                          onClick={() => setViewMode('frames')}
                          className="bg-muted hover:bg-accent text-muted-foreground hover:text-accent-foreground px-3 py-2 rounded text-sm font-medium transition-colors flex items-center gap-1"
                        >
                          <Film className="w-4 h-4" />
                          View frames
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Frames View */}
              {(viewMode === 'frames' || searchClips.length === 0) && (
                <div className="max-h-96 overflow-y-auto space-y-3 pr-2 scrollbar-thin scrollbar-thumb-gray-500/50 scrollbar-track-transparent">
                  {searchResults.map((result, index) => (
                    <div
                      key={index}
                      className="bg-gray-900/80 rounded-lg border border-gray-700 p-4 hover:bg-gray-800/80 transition-all duration-300 cursor-pointer group"
                    onClick={() => onTimeJump(result.timestamp)}
                  >
                    <div className="flex gap-4">
                        {/* Frame Number & Thumbnail */}
                        <div className="flex flex-col items-center gap-2 flex-shrink-0">
                          <div className="w-20 h-12 bg-gray-800 rounded flex items-center justify-center relative overflow-hidden">
                            {result.frame_path && result.frame_path !== '/api/placeholder/120/68' ? (
                              <img
                                src={result.frame_path}
                                alt={`Frame at ${formatTime(result.timestamp)}`}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const target = e.currentTarget as HTMLImageElement;
                                  target.style.display = 'none';
                                  const fallback = target.nextElementSibling as HTMLElement;
                                  if (fallback) fallback.style.display = 'flex';
                                }}
                              />
                            ) : null}
                            <div
                              className="w-full h-full flex items-center justify-center"
                              style={{ display: (result.frame_path && result.frame_path !== '/api/placeholder/120/68') ? 'none' : 'flex' }}
                            >
                        <Eye className="w-6 h-6 text-gray-400" />
                            </div>
                          </div>
                          <span className="text-xs text-gray-400 font-medium">#{index + 1}</span>
                      </div>

                      {/* Content */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                            <div className="flex-1 pr-2">
                              {/* Summary (concise) */}
                              {result.summary ? (
                                <p className="text-white text-sm font-medium mb-1">
                                  {result.summary}
                                </p>
                              ) : (
                                <p className="text-white text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                            {result.description}
                          </p>
                              )}

                              {/* Objects detected */}
                              {result.objects_detected && result.objects_detected.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {result.objects_detected.slice(0, 3).map((obj, objIndex) => (
                                    <span
                                      key={objIndex}
                                      className="inline-block px-2 py-1 bg-gray-800 rounded text-xs text-gray-300 border border-gray-600"
                                    >
                                      {obj}
                                    </span>
                                  ))}
                                  {result.objects_detected.length > 3 && (
                                    <span className="text-xs text-gray-400">
                                      +{result.objects_detected.length - 3} more
                                    </span>
                                  )}
                                </div>
                              )}
                            </div>
                          <div className="flex items-center gap-2 text-xs">
                              <span className={`font-medium px-2 py-1 rounded ${getConfidenceColor(result.confidence)} bg-gray-800`}>
                              {result.confidence}%
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-xs text-gray-400">
                            <Clock className="w-3 h-3" />
                              <span className="font-medium">{formatTime(result.timestamp)}</span>
                              {result.people_count !== undefined && (
                                <>
                                  <span className="mx-1">•</span>
                                  <Users className="w-3 h-3" />
                                  <span>{result.people_count} person{result.people_count !== 1 ? 's' : ''}</span>
                                </>
                              )}
                          </div>
                            <div className="flex items-center gap-2">
                              {/* Expand/Collapse detailed analysis */}
                              {result.detailed_analysis && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    toggleExpandResult(index);
                                  }}
                                  className="opacity-0 group-hover:opacity-100 transition-opacity bg-gray-800 hover:bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs font-medium flex items-center gap-1"
                                >
                                  {expandedResults.has(index) ? (
                                    <>
                                      <ChevronUp className="w-3 h-3" />
                                      Less
                                    </>
                                  ) : (
                                    <>
                                      <ChevronDown className="w-3 h-3" />
                                      Details
                                    </>
                                  )}
                                </button>
                              )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onTimeJump(result.timestamp);
                            }}
                                className="opacity-0 group-hover:opacity-100 transition-opacity bg-white hover:bg-gray-200 text-black px-3 py-1 rounded text-xs font-medium flex items-center gap-1"
                          >
                                <Eye className="w-3 h-3" />
                            Jump to frame
                          </button>
                        </div>
                          </div>

                          {/* Expanded detailed analysis */}
                          {expandedResults.has(index) && result.detailed_analysis && (
                            <div className="mt-3 pt-3 border-t border-gray-700">
                              <p className="text-sm text-gray-300 leading-relaxed">
                                {result.detailed_analysis}
                              </p>
                            </div>
                          )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              )}
            </>
          ) : (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-white mb-2">
                No matches found for "{searchQuery}"
              </p>
              <p className="text-sm text-gray-400">
                Try different keywords or be more specific about visual elements
              </p>
            </div>
          )}
        </div>
      )}


    </div>
  );
};

export default VisualSearch;
