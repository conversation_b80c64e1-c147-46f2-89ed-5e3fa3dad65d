import React, { useState } from 'react';
import { Search, Eye, Clock, Zap, Film, ChevronDown, ChevronUp, Users, Target } from 'lucide-react';
import { SearchResult, ClipResult, apiService } from '@/services/api';
import { useToast } from '@/hooks/use-toast';

interface VisualSearchProps {
  videoId: number;
  videoUrl: string;
  onTimeJump: (time: number) => void;
}

const VisualSearch: React.FC<VisualSearchProps> = ({ videoId, onTimeJump }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchClips, setSearchClips] = useState<ClipResult[]>([]);
  const [hasSearched, setHasSearched] = useState(false);
  const [viewMode, setViewMode] = useState<'clips' | 'frames'>('clips');
  const [directAnswer, setDirectAnswer] = useState<string>('');
  const [queryType, setQueryType] = useState<string>('');
  const [expandedResults, setExpandedResults] = useState<Set<number>>(new Set());

  const { toast } = useToast();

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setHasSearched(true);
    setExpandedResults(new Set()); // Reset expanded results

    try {
      // Use real backend API only
      const response = await apiService.visualSearch(videoId, searchQuery);

      // Check if backend returns enhanced format
      if (response.direct_answer || response.query_type || response.clips) {
        // Backend supports enhanced format
        setSearchResults(response.results);
        setSearchClips(response.clips || []);
        setDirectAnswer(response.direct_answer || '');
        setQueryType(response.query_type || '');
      } else {
        // Backend returns old format, convert to enhanced
        setSearchResults(response.results.map(result => ({
          ...result,
          summary: result.description.length > 100 ? 
            result.description.substring(0, 100) + '...' : 
            result.description,
          detailed_analysis: result.description
        })));
        setSearchClips([]);
        setDirectAnswer(response.results.length > 0 ? 
          `Found ${response.results.length} matches for "${searchQuery}"` : '');
        setQueryType('general');
      }

      if (response.results.length === 0) {
        toast({
          title: "No results found",
          description: `No visual content matching "${searchQuery}" was found in this video.`,
        });
      }
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Search failed",
        description: error instanceof Error ? error.message : "Failed to search video content",
        variant: "destructive",
      });

      // Clear results on error - no mock fallback
      setSearchResults([]);
      setSearchClips([]);
      setDirectAnswer('');
      setQueryType('');
    } finally {
      setIsSearching(false);
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 90) return 'text-green-400';
    if (confidence >= 75) return 'text-yellow-400';
    return 'text-orange-400';
  };

  const toggleExpandResult = (index: number) => {
    const newExpanded = new Set(expandedResults);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedResults(newExpanded);
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-foreground mb-2">Visual Search</h3>
        <p className="text-sm text-muted-foreground">
          Search for objects, people, or scenes within video frames
        </p>
      </div>

      {/* Search Form */}
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Describe what you're looking for... (e.g., 'red car', 'person speaking', 'computer screen')"
            className="w-full px-4 py-3 pl-12 bg-input border border-border rounded-lg text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
            disabled={isSearching}
          />
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
        </div>

        <button
          type="submit"
          disabled={!searchQuery.trim() || isSearching}
          className="w-full bg-primary hover:bg-primary/90 disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed text-primary-foreground font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center gap-2"
        >
          {isSearching ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-primary-foreground border-t-transparent"></div>
              Analyzing Frames...
            </>
          ) : (
            <>
              <Eye className="w-5 h-5" />
              Search Video
            </>
          )}
        </button>
      </form>

      {/* Search Results */}
      {hasSearched && (
        <div className="space-y-4">
          {isSearching ? (
            <div className="text-center py-8">
              <div className="animate-pulse space-y-4">
                <div className="w-16 h-16 bg-gray-800 rounded-full mx-auto flex items-center justify-center">
                  <Zap className="w-8 h-8 text-white" />
                </div>
                <p className="text-gray-300">
                  AI is analyzing video frames for "{searchQuery}"
                </p>
              </div>
            </div>
          ) : searchResults.length > 0 ? (
            <>
              {/* Direct Answer Section */}
              {directAnswer && (
                <div className="mb-4 p-4 bg-gray-900/80 rounded-lg border border-gray-700">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center flex-shrink-0">
                      {queryType === 'counting' ? (
                        <Users className="w-4 h-4 text-black" />
                      ) : (
                        <Target className="w-4 h-4 text-black" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-white font-medium mb-1">Direct Answer</h4>
                      <p className="text-gray-300 text-sm leading-relaxed">{directAnswer}</p>
                      {queryType && (
                        <span className="inline-block mt-2 px-2 py-1 bg-gray-800 rounded text-xs text-gray-400 border border-gray-600">
                          {queryType.replace('_', ' ')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Results Header with View Toggle */}
              <div className="flex items-center justify-between mb-4">
                <div>
                <h4 className="text-white font-medium">
                    Found {searchResults.length} {searchResults.length === 1 ? 'match' : 'matches'} for "{searchQuery}"
                </h4>
                  <p className="text-sm text-gray-400 mt-1">
                    {searchResults.length > 1 ? 'Multiple instances detected throughout the video. ' : ''}Click any result to jump to that moment in the video
                  </p>
                </div>
                {searchClips.length > 0 && (
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setViewMode('clips')}
                      className={`px-3 py-1 rounded text-sm font-medium transition-all ${
                        viewMode === 'clips'
                          ? 'bg-white text-black'
                          : 'bg-gray-800 text-gray-400 hover:bg-gray-700 hover:text-white'
                      }`}
                    >
                      <Film className="w-4 h-4 inline mr-1" />
                      Clips ({searchClips.length})
                    </button>
                    <button
                      onClick={() => setViewMode('frames')}
                      className={`px-3 py-1 rounded text-sm font-medium transition-all ${
                        viewMode === 'frames'
                          ? 'bg-white text-black'
                          : 'bg-gray-800 text-gray-400 hover:bg-gray-700 hover:text-white'
                      }`}
                    >
                      <Eye className="w-4 h-4 inline mr-1" />
                      Frames ({searchResults.length})
                    </button>
                  </div>
                )}
              </div>

              {/* Clips View */}
              {viewMode === 'clips' && searchClips.length > 0 && (
                <div className="max-h-96 overflow-y-auto space-y-3 pr-2 scrollbar-thin scrollbar-thumb-gray-500/50 scrollbar-track-transparent">
                  {searchClips.map((clip, index) => (
                    <div
                      key={index}
                      className="bg-card rounded-lg border border-border p-4 hover:bg-muted/50 transition-all duration-300"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1 pr-4">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-xs text-muted-foreground font-medium">#{index + 1}</span>
                            <h5 className="text-foreground font-medium">
                              {formatTime(clip.start_time)} - {formatTime(clip.end_time)}
                            </h5>
                            <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                              {Math.round((clip.end_time - clip.start_time) * 10) / 10}s
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground leading-relaxed">
                            {clip.description}
                          </p>
                        </div>
                        <div className="flex flex-col items-end gap-1">
                          <span className={`text-sm font-medium px-2 py-1 rounded ${getConfidenceColor(clip.confidence)} bg-muted`}>
                            {clip.confidence}%
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {clip.frame_count} frames
                          </span>
                        </div>
                      </div>

                      {/* Clip Actions */}
                      <div className="flex gap-2">
                        <button
                          onClick={() => onTimeJump(clip.start_time)}
                          className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-2 rounded text-sm font-medium transition-colors flex items-center justify-center gap-1"
                        >
                          <Eye className="w-4 h-4" />
                          Play from start
                        </button>
                        <button
                          onClick={() => setViewMode('frames')}
                          className="bg-muted hover:bg-accent text-muted-foreground hover:text-accent-foreground px-3 py-2 rounded text-sm font-medium transition-colors flex items-center gap-1"
                        >
                          <Film className="w-4 h-4" />
                          View frames
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Frames View */}
              {(viewMode === 'frames' || searchClips.length === 0) && (
                <div className="max-h-96 overflow-y-auto space-y-3 pr-2 scrollbar-thin scrollbar-thumb-gray-500/50 scrollbar-track-transparent">
                  {searchResults.map((result, index) => (
                    <div
                      key={index}
                      className="bg-gray-900/80 rounded-lg border border-gray-700 p-4 hover:bg-gray-800/80 transition-all duration-300 cursor-pointer group"
                    onClick={() => onTimeJump(result.timestamp)}
                  >
                    <div className="flex gap-4">
                        {/* Frame Number & Thumbnail */}
                        <div className="flex flex-col items-center gap-2 flex-shrink-0">
                          <div className="w-20 h-12 bg-gray-800 rounded flex items-center justify-center relative overflow-hidden">
                            {result.frame_path && result.frame_path.startsWith('/') ? (
                              <img
                                src={result.frame_path}
                                alt={`Frame at ${formatTime(result.timestamp)}`}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const target = e.currentTarget as HTMLImageElement;
                                  target.style.display = 'none';
                                  const fallback = target.nextElementSibling as HTMLElement;
                                  if (fallback) fallback.style.display = 'flex';
                                }}
                              />
                            ) : null}
                            <div
                              className="w-full h-full flex items-center justify-center"
                              style={{ display: (result.frame_path && result.frame_path.startsWith('/')) ? 'none' : 'flex' }}
                            >
                        <Eye className="w-6 h-6 text-gray-400" />
                            </div>
                          </div>
                          <span className="text-xs text-gray-400 font-medium">#{index + 1}</span>
                      </div>

                      {/* Content */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                            <div className="flex-1 pr-2">
                              {/* Summary (concise) */}
                              {result.summary ? (
                                <p className="text-white text-sm font-medium mb-1">
                                  {result.summary}
                                </p>
                              ) : (
                                <p className="text-white text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                            {result.description}
                          </p>
                              )}

                              {/* Objects detected */}
                              {result.objects_detected && result.objects_detected.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {result.objects_detected.slice(0, 3).map((obj, objIndex) => (
                                    <span
                                      key={objIndex}
                                      className="inline-block px-2 py-1 bg-gray-800 rounded text-xs text-gray-300 border border-gray-600"
                                    >
                                      {obj}
                                    </span>
                                  ))}
                                  {result.objects_detected.length > 3 && (
                                    <span className="text-xs text-gray-400">
                                      +{result.objects_detected.length - 3} more
                                    </span>
                                  )}
                                </div>
                              )}
                            </div>
                          <div className="flex items-center gap-2 text-xs">
                              <span className={`font-medium px-2 py-1 rounded ${getConfidenceColor(result.confidence)} bg-gray-800`}>
                              {result.confidence}%
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-xs text-gray-400">
                            <Clock className="w-3 h-3" />
                              <span className="font-medium">{formatTime(result.timestamp)}</span>
                              {result.people_count !== undefined && (
                                <>
                                  <span className="mx-1">•</span>
                                  <Users className="w-3 h-3" />
                                  <span>{result.people_count} person{result.people_count !== 1 ? 's' : ''}</span>
                                </>
                              )}
                          </div>
                            <div className="flex items-center gap-2">
                              {/* Expand/Collapse detailed analysis */}
                              {result.detailed_analysis && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    toggleExpandResult(index);
                                  }}
                                  className="opacity-0 group-hover:opacity-100 transition-opacity bg-gray-800 hover:bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs font-medium flex items-center gap-1"
                                >
                                  {expandedResults.has(index) ? (
                                    <>
                                      <ChevronUp className="w-3 h-3" />
                                      Less
                                    </>
                                  ) : (
                                    <>
                                      <ChevronDown className="w-3 h-3" />
                                      Details
                                    </>
                                  )}
                                </button>
                              )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onTimeJump(result.timestamp);
                            }}
                                className="opacity-0 group-hover:opacity-100 transition-opacity bg-white hover:bg-gray-200 text-black px-3 py-1 rounded text-xs font-medium flex items-center gap-1"
                          >
                                <Eye className="w-3 h-3" />
                            Jump to frame
                          </button>
                        </div>
                          </div>

                          {/* Expanded detailed analysis */}
                          {expandedResults.has(index) && result.detailed_analysis && (
                            <div className="mt-3 pt-3 border-t border-gray-700">
                              <p className="text-sm text-gray-300 leading-relaxed">
                                {result.detailed_analysis}
                              </p>
                            </div>
                          )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              )}
            </>
          ) : (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-white mb-2">
                No matches found for "{searchQuery}"
              </p>
              <p className="text-sm text-gray-400">
                Try different keywords or be more specific about visual elements
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default VisualSearch;
