import React, { useState, useEffect } from 'react';
import { Clock, Play, Hash } from 'lucide-react';
import { apiService, VideoSection } from '@/services/api';
import { useToast } from '@/hooks/use-toast';

interface VideoSectionsProps {
  videoId: number;
  videoUrl: string;
  videoDuration?: number; // Add video duration prop
  onTimeJump: (time: number) => void;
}

const VideoSections: React.FC<VideoSectionsProps> = ({ videoId, videoUrl, videoDuration, onTimeJump }) => {
  const [sections, setSections] = useState<VideoSection[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Calculate total video duration for timeline calculations
  const totalDuration = videoDuration || 420; // Default to 7 minutes if not provided

  useEffect(() => {
    const fetchSections = async () => {
      try {
        setLoading(true);
        const response = await apiService.getVideoSections(videoId);
        setSections(response.sections || []);
      } catch (error) {
        console.error('Error loading sections:', error);
        toast({
          title: "Error loading sections",
          description: error instanceof Error ? error.message : "Failed to load video sections",
          variant: "destructive",
        });
        // Clear sections on error - no mock fallback
        setSections([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSections();
  }, [videoId, toast]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const parseTimeToSeconds = (timeStr: string | number): number => {
    if (typeof timeStr === 'number') {
      return timeStr;
    }
    const parts = timeStr.split(':');
    if (parts.length === 2) {
      return parseInt(parts[0]) * 60 + parseInt(parts[1]);
    }
    return 0;
  };

  const getDuration = (startTime: number, endTime: number): string => {
    const duration = endTime - startTime;
    return `${Math.floor(duration / 60)}:${(duration % 60).toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="h-full flex flex-col bg-black">
        <div className="text-center mb-4 flex-shrink-0">
          <h3 className="text-lg font-semibold text-white mb-2">Video Sections</h3>
          <p className="text-sm text-gray-300">Loading sections...</p>
        </div>
        <div className="flex-1 animate-pulse space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-gray-900/80 rounded-lg p-4 h-24 border border-gray-700"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-black">
      {/* Header */}
      <div className="text-center mb-4 flex-shrink-0">
        <h3 className="text-lg font-semibold text-white mb-2">Video Sections</h3>
        <p className="text-sm text-gray-300">
          AI-generated breakdown with clickable timestamps
        </p>
      </div>

      {/* Sections List - Scrollable */}
      <div className="flex-1 overflow-y-auto space-y-3 pr-2 scrollbar-thin scrollbar-thumb-gray-500/50 scrollbar-track-transparent">
        {sections.length > 0 ? (
          sections.map((section, index) => (
            <div
              key={section.id}
              className="bg-gray-900/80 rounded-lg border border-gray-700 p-4 hover:bg-gray-800/80 transition-all duration-300 cursor-pointer group"
              onClick={() => onTimeJump(parseTimeToSeconds(section.start_time))}
            >
              {/* Section Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center text-black font-semibold text-sm">
                    {index + 1}
                  </div>
                  <div>
                    <h4 className="font-medium text-white group-hover:text-gray-300 transition-colors">
                      {section.title}
                    </h4>
                    <div className="flex items-center gap-4 text-xs text-gray-400 mt-1">
                      <span className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {typeof section.start_time === 'number' ? formatTime(section.start_time) : section.start_time} - {typeof section.end_time === 'number' ? formatTime(section.end_time) : section.end_time}
                      </span>
                      <span>Duration: {getDuration(parseTimeToSeconds(section.start_time), parseTimeToSeconds(section.end_time))}</span>
                    </div>
                  </div>
                </div>

                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onTimeJump(parseTimeToSeconds(section.start_time));
                  }}
                  className="opacity-0 group-hover:opacity-100 transition-opacity bg-white hover:bg-gray-200 text-black p-2 rounded-full transform hover:scale-110 transition-transform"
                >
                  <Play className="w-4 h-4" />
                </button>
              </div>

              {/* Section Description */}
              <p className="text-sm text-gray-300 mb-3 leading-relaxed">
                {section.description}
              </p>

              {/* Key Topics */}
              <div className="flex flex-wrap gap-2">
                {section.key_topics.map((topic, topicIndex) => (
                  <span
                    key={topicIndex}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-gray-800 rounded text-xs text-gray-300 border border-gray-600"
                  >
                    <Hash className="w-3 h-3" />
                    {topic}
                  </span>
                ))}
              </div>

              {/* Progress Bar */}
              <div className="mt-3 h-1 bg-gray-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-white rounded-full"
                  style={{
                    width: `${((parseTimeToSeconds(section.end_time) - parseTimeToSeconds(section.start_time)) / totalDuration) * 100}%`
                  }}
                />
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-white mb-2">No sections available</p>
            <p className="text-sm text-gray-400">
              Video sections couldn't be loaded. Please try refreshing or check that the video has been processed.
            </p>
          </div>
        )}
      </div>

      {/* Timeline Overview */}
      <div className="mt-4 p-4 bg-gray-900/80 rounded-lg border border-gray-700 flex-shrink-0">
        <h4 className="text-sm font-medium text-white mb-3">Timeline Overview</h4>
        <div className="relative h-2 bg-gray-700 rounded-full">
          {sections.map((section) => (
            <div
              key={section.id}
              className="absolute top-0 h-full bg-white rounded-full cursor-pointer hover:opacity-80 transition-opacity"
              style={{
                left: `${(parseTimeToSeconds(section.start_time) / totalDuration) * 100}%`,
                width: `${((parseTimeToSeconds(section.end_time) - parseTimeToSeconds(section.start_time)) / totalDuration) * 100}%`,
              }}
              onClick={() => onTimeJump(parseTimeToSeconds(section.start_time))}
              title={`${section.title} (${typeof section.start_time === 'number' ? formatTime(section.start_time) : section.start_time})`}
            />
          ))}
        </div>
        <div className="flex justify-between text-xs text-gray-400 mt-2">
          <span>0:00</span>
          <span>{Math.floor(totalDuration / 60)}:{(totalDuration % 60).toString().padStart(2, '0')}</span>
        </div>
      </div>
    </div>
  );
};

export default VideoSections;
